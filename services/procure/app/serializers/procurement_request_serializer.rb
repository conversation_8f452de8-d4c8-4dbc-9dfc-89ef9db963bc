class ProcurementRequestSerializer
  include JSONAPI::Serializer

  attributes :quantity, :note, :status, :project_id, :submitted_at, :created_at, :updated_at

  belongs_to :item, serializer: ItemSerializer

  belongs_to :requester, record_type: :remote_user, serializer: RemoteUserSerializer

  belongs_to :approval_request, serializer: ApprovalRequestSerializer, optional: true

  belongs_to :project, record_type: :remote_project, serializer: RemoteProjectSerializer

  attribute :total_cost do |object|
    return 0.0 unless object.item&.approx_price

    (object.item.approx_price * object.quantity).to_f
  end



  # Computed permissions and available actions
  attribute :permissions do |object|
    {
      can_cancel: object.can_be_cancelled?,
      can_process: object.approved?,
      can_deliver: object.processing?,
      is_editable: false, # Procurement requests are never editable once created
      available_actions: [
        ("cancel" if object.can_be_cancelled?),
        ("approve" if object.status.in?(%w[submitted under_review])),
        ("reject" if object.status.in?(%w[submitted under_review])),
        ("mark_processing" if object.approved?),
        ("mark_delivered" if object.processing?)
      ].compact
    }
  end
end
