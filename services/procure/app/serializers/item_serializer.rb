class ItemSerializer
  include JSONAPI::Serializer

  attributes :name, :description, :category, :approx_price, :project_id, :created_at, :updated_at

  belongs_to :creator, record_type: :remote_user, serializer: RemoteUserSerializer, id_method_name: :created_by_id

  belongs_to :project, record_type: :remote_project, serializer: RemoteProjectSerializer

  attribute :procurement_requests_count do |object|
    object.procurement_requests.count
  end

  attribute :total_requested_quantity do |object|
    object.procurement_requests.sum(:quantity)
  end

  attribute :average_request_quantity do |object|
    count = object.procurement_requests.count
    if count.zero?
      0
    else
      object.procurement_requests.sum(:quantity).to_f / count
    end
  end
end
