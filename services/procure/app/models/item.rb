class Item < ApplicationRecord
  include Athar::Commons::Models::Concerns::Ransackable
  include Athar::Commons::ActiveStruct::ActiveRecordAssociations

  # Core attributes
  has_many :procurement_requests

  # Remote associations via RPC
  rpc_belongs_to :creator, foreign_key: :created_by_id, class_name: "RemoteUser"
  rpc_belongs_to :project, foreign_key: :project_id, class_name: "RemoteProject"

  # Validations
  validates :name, presence: true
  validates :category, presence: true
  validates :approx_price, presence: true, numericality: { greater_than_or_equal_to: 0 }
  # validates :created_by_id, presence: true  # Commented out for now due to User model issues
  validates :project_id, presence: true

  # Scopes
  scope :by_category, ->(cat) { where(category: cat) if cat.present? }
  scope :for_project, ->(project_id) { where(project_id: project_id) }
end
