class ApprovalAction < ApplicationRecord
  include Athar::Commons::Models::Concerns::ApprovalActionCallbacks
  include Athar::Commons::ActiveStruct::ActiveRecordAssociations

  belongs_to :approval_request
  belongs_to :approval_step

  # RPC association for user data from Core service
  rpc_belongs_to :user, foreign_key: :user_id, class_name: "RemoteUser"

  validates :user_id, presence: true
  validates :action, presence: true, inclusion: { in: %w[approve reject comment] }
  validates :user_id, uniqueness: { scope: [ :approval_request_id, :approval_step_id, :action ],
                                    message: "has already performed this action" },
            if: -> { %w[approve reject].include?(action) }
end
