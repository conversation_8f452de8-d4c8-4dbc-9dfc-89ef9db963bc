class ProcurementRequest < ApplicationRecord
  Rails.logger.info "Loading ProcurementRequest model"
  include Athar::Commons::Models::Concerns::ActsAsApprovable
  include Athar::Commons::Models::Concerns::Ransackable
  include Athar::Commons::ActiveStruct::ActiveRecordAssociations

  belongs_to :item

  # RPC associations for remote data from Core service
  rpc_belongs_to :requester, foreign_key: :requester_id, class_name: "RemoteUser"
  rpc_belongs_to :project, foreign_key: :project_id, class_name: "RemoteProject"

  # Multi-step approval workflow integration
  acts_as_approvable on_status_change: :handle_approval_status_change

  # Status management (no draft status - requests are created and submitted immediately)
  # Status values: submitted, under_review, approved, rejected, cancelled, processing, delivered
  enum :status, {
    submitted: "submitted",
    under_review: "under_review",
    approved: "approved",
    rejected: "rejected",
    cancelled: "cancelled",
    processing: "processing",
    delivered: "delivered"
  }

  # Default status - will be updated by approval workflow
  attribute :status, default: "submitted"

  # Project scoping (both items and procurement requests are project-scoped)
  validates :project_id, presence: true
  validates :quantity, numericality: { greater_than: 0 }
  scope :for_project, ->(project_id) { where(project_id: project_id) }

  # Ensure item and request belong to same project
  validate :item_project_matches_request_project

  # Prevent updates once approval workflow has started
  validate :prevent_updates_after_submission, on: :update

  # Scopes
  scope :pending, -> { where(status: "submitted") }
  scope :approved, -> { where(status: "approved") }
  scope :by_user, ->(user_id) { where(requester_id: user_id) }

  # Trigger approval workflow automatically on creation
  after_create :trigger_approval_workflow
  Rails.logger.info "Registered after_create callback for ProcurementRequest"

  # External procurement status updates
  def mark_as_processing!
    return false unless approved?
    update!(status: :processing)
  end

  def mark_as_delivered!
    return false unless processing?
    update!(status: :delivered)
  end

  # Cancellation methods
  def can_be_cancelled?
    status.in?(%w[submitted under_review])
  end

  def cancel_request!(user_id, reason: nil)
    return OpenStruct.new(success?: false, message: "Request cannot be cancelled in current status") unless can_be_cancelled?

    begin
      transaction do
        # Cancel the approval workflow if it exists
        if approval_request&.pending?
          approval_request.cancel!(user_id, comment: reason)
        end

        # Update the procurement request status
        update!(status: :cancelled)
      end

      OpenStruct.new(success?: true, message: "Request cancelled successfully")
    rescue => e
      OpenStruct.new(success?: false, message: "Failed to cancel request: #{e.message}")
    end
  end

  # Required by approval system - use existing workflow from core service
  def approval_action
    "buy_new_item_context" # Uses context-aware workflow with amount and priority
  end

  def system_name
    "procure"
  end

  private

  def build_approval_context
    # Context for the approval workflow generation (sent to Core service)
    # This matches the required_context_keys for "buy_new_item_context" workflow
    {
      amount: (item.approx_price * quantity).to_s, # Required by workflow
      priority: determine_priority, # Required by workflow
      item_category: item.category,
      quantity: quantity,
      project_id: project_id,
      requester_role: requester.role&.name
    }
  end

  def determine_priority
    # Business logic to determine priority
    total_amount = item.approx_price * quantity

    if total_amount > 5000
      "high"
    elsif total_amount > 1000
      "medium"
    else
      "low"
    end
  end

  def handle_approval_status_change
    case approval_workflow_status
    when "approved"
      update!(status: :approved)
      # Approved requests are ready for external procurement
    when "rejected"
      update!(status: :rejected)
    end
  end

  private

  def trigger_approval_workflow
    Rails.logger.info "=== TRIGGER_APPROVAL_WORKFLOW called for ProcurementRequest ##{id} ==="
    Rails.logger.info "Requester ID: #{requester_id}, Project ID: #{project_id}"
    Rails.logger.info "Approval context: #{build_approval_context}"

    result = submit_for_approval(requester_id, project_id, build_approval_context)
    Rails.logger.info "Submit for approval result: #{result}"

    if result
      Rails.logger.info "Updating status to under_review"
      update!(status: :under_review)
      # Reload the approval_request association to ensure it's available for serialization
      reload_approval_request
    else
      Rails.logger.error "Failed to submit for approval"
      Rails.logger.error "Approval error: #{approval_error}" if respond_to?(:approval_error)
    end
  end

  def item_project_matches_request_project
    return unless item && project_id

    if item.project_id != project_id
      errors.add(:item, "must belong to the same project as the request")
    end
  end

  def prevent_updates_after_submission
    # Allow status changes for workflow progression
    return if only_status_changed?

    # Allow updates only if the request is still in submitted status or cancelled
    unless status == "submitted" || status == "cancelled"
      errors.add(:base, "Cannot update procurement request after approval workflow has started")
    end

    # Prevent changing critical fields that affect approval workflow
    if item_id_changed? || quantity_changed?
      errors.add(:base, "Cannot change item or quantity after submission")
    end
  end

  def only_status_changed?
    return false unless persisted?
    changed_attributes.keys == [ "status" ] || changed_attributes.keys.empty?
  end
end
