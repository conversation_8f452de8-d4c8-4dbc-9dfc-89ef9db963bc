#!/usr/bin/env ruby

puts "=== Testing Sort by ID Fix ==="
puts

# Test 1: Check if 'id' is in ransackable_attributes
puts "1. Testing ransackable_attributes includes 'id'..."
ransackable_attrs = Case.ransackable_attributes
if ransackable_attrs.include?('id')
  puts "✅ PASS: 'id' is in ransackable_attributes"
  puts "   Position: #{ransackable_attrs.index('id') + 1} of #{ransackable_attrs.length}"
else
  puts "❌ FAIL: 'id' is NOT in ransackable_attributes"
  puts "   Current attributes: #{ransackable_attrs.join(', ')}"
end
puts

# Test 2: Test Ransack search with id sorting
puts "2. Testing Ransack search with id sorting..."
begin
  # Test ascending sort
  asc_search = Case.ransack(s: 'id asc')
  asc_results = asc_search.result.limit(3)
  puts "✅ PASS: Ascending sort by id works (#{asc_results.count} results)"
  
  # Test descending sort  
  desc_search = Case.ransack(s: 'id desc')
  desc_results = desc_search.result.limit(3)
  puts "✅ PASS: Descending sort by id works (#{desc_results.count} results)"
  
  if asc_results.any? && desc_results.any?
    puts "   First ID (asc): #{asc_results.first.id}"
    puts "   First ID (desc): #{desc_results.first.id}"
  end
rescue => e
  puts "❌ FAIL: Ransack sorting failed: #{e.message}"
end
puts

# Test 3: Test API endpoint simulation
puts "3. Testing API filtering simulation..."
begin
  # Simulate what the API controller does
  cases = Case.all
  
  # Test the apply_filters method if available
  if defined?(ApplicationController) && ApplicationController.new.respond_to?(:apply_filters, true)
    puts "✅ apply_filters method is available"
  else
    puts "⚠️  apply_filters method not directly testable"
  end
  
  # Test manual ransack with sort parameter
  sort_param = '-id'  # This is what comes from ?sort=-id
  ransack_sort = sort_param.gsub('-', '') + ' desc'
  
  search = cases.ransack(s: ransack_sort)
  sorted_cases = search.result.limit(5)
  
  if sorted_cases.any?
    puts "✅ PASS: Manual sort simulation works"
    puts "   Top 3 IDs (desc): #{sorted_cases.limit(3).pluck(:id).join(', ')}"
  else
    puts "❌ FAIL: Manual sort simulation failed"
  end
rescue => e
  puts "❌ FAIL: API simulation failed: #{e.message}"
end
puts

puts "=== Test Summary ==="
puts "The fix should now allow sorting by 'id' in API requests like ?sort=-id"
puts "Check the API endpoint directly to confirm the fix works end-to-end."
