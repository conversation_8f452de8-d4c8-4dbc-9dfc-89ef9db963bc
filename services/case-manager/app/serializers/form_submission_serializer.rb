class FormSubmissionSerializer
  include JSONAPI::Serializer

  # Basic attributes
  attributes :case_id, :form_template_id, :form_section_id, :form_data, :status,
             :submitted_at, :completed_at, :created_by_id, :updated_by_id,
             :created_at, :updated_at

  # Computed attributes
  attribute :completion_percentage do |object|
    object.completion_percentage
  end

  attribute :can_be_submitted do |object|
    object.can_be_submitted?
  end

  attribute :is_draft do |object|
    object.form_draft?
  end

  attribute :is_in_progress do |object|
    object.form_in_progress?
  end

  attribute :is_submitted do |object|
    object.form_submitted?
  end

  attribute :is_completed do |object|
    object.form_completed?
  end

  attribute :validation_errors do |object|
    object.validate_form_data
  end

  attribute :section_completion_status do |object|
    object.section_completion_status
  end

  # Relationships
  belongs_to :case, serializer: CaseSerializer
  belongs_to :form_template, serializer: FormTemplateSerializer
  belongs_to :form_section, serializer: FormSectionSerializer, optional: true

  # User relationships (using RemoteUserSerializer for GRPC users)
  belongs_to :created_by, record_type: :remote_user, serializer: RemoteUserSerializer, id_method_name: :created_by_id
  belongs_to :updated_by, record_type: :remote_user, serializer: RemoteUserSerializer, id_method_name: :updated_by_id, optional: true
end
